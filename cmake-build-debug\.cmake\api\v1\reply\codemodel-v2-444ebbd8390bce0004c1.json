{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.25.1"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "XDP_FiveM", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "bpf_program::@6890427a1f51a3e7e1df", "jsonFile": "target-bpf_program-Debug-30aff7639de603df1ccf.json", "name": "bpf_program", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/root/XDP-FiveM/cmake-build-debug", "source": "/root/XDP-FiveM"}, "version": {"major": 2, "minor": 4}}