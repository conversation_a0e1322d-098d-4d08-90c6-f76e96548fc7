{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}]}, "id": "bpf_program::@6890427a1f51a3e7e1df", "name": "bpf_program", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "cmake-build-debug/CMakeFiles/bpf_program", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/CMakeFiles/bpf_program.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/fivem_xdp.o.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}