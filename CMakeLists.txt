cmake_minimum_required(VERSION 3.25.1)
project(XDP_FiveM C)

set(CMAKE_C_STANDARD 11)

add_custom_command(
        OUTPUT fivem_xdp.o
        COMMAND clang -O2 -g -target bpf -c fivem_xdp.c -o fivem_xdp.o -I/usr/include -I/usr/include/$(uname -m)-linux-gnu
        DEPENDS fivem_xdp.c
        COMMENT "Compiling fivem_xdp.c to BPF object file"
)

# This custom target allows us to trigger the command above by building the "bpf_program" target.
add_custom_target(
        bpf_program ALL
        DEPENDS fivem_xdp.o
)