{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.25"}, "version": {"isDirty": false, "major": 3, "minor": 25, "patch": 1, "string": "3.25.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-444ebbd8390bce0004c1.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "cache-v2-e989be3e125fbe709602.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-eadc622acc3ddd913aa5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-6ce52b7e4da806b64a6b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-e989be3e125fbe709602.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-eadc622acc3ddd913aa5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-444ebbd8390bce0004c1.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, "toolchains-v1": {"jsonFile": "toolchains-v1-6ce52b7e4da806b64a6b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}